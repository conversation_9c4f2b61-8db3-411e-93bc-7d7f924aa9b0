using System;

namespace Odrc.Dots.Business
{
    public static class UserLogic
    {
        public static UserInfo GetUserById(string userId)
        {
            // For now, return a mock user object
            // In a real implementation, this would query a database or user service
            if (string.IsNullOrWhiteSpace(userId))
            {
                return null;
            }

            return new UserInfo
            {
                UserId = userId,
                UserName = userId, // Using userId as username for simplicity
                FullName = "System User",
                IsActive = true
            };
        }
    }

    public class UserInfo
    {
        public string UserId { get; set; } = "";
        public string UserName { get; set; } = "";
        public string FullName { get; set; } = "";
        public bool IsActive { get; set; }
    }
}
